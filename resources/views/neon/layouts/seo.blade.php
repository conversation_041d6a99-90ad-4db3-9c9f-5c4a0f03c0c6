<!DOCTYPE html>
<html lang="{{ str_replace('_','-',strtolower(app()->getLocale())) }}">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" />
    <title>{{ $page_title ?? '' }} | {{ dujiaoka_config_get('title') }}</title>
    <meta name="keywords" content="{{ $gd_keywords ?? '' }}">
    <meta name="description" content="{{ $gd_description ?? '' }}">
    <!-- 省略其它meta和link... -->
    @if(\request()->getScheme() == "https")
        <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
    @endif
    <link rel="apple-touch-icon" href="/assets/neon/app-icons/icon-180x180.png" />
    <script src="/assets/neon/js/theme-switcher.js"></script>
    <link rel="preload" href="/assets/neon/fonts/inter-variable-latin.woff2" as="font" type="font/woff2" crossorigin />
    <link rel="preload" href="/assets/neon/icons/cartzilla-icons.woff2" as="font" type="font/woff2" crossorigin />
    <link rel="stylesheet" href="/assets/neon/icons/cartzilla-icons.min.css" />
    <link rel="stylesheet" href="/assets/neon/css/swiper-bundle.min.css" />
    <link rel="stylesheet" href="/assets/neon/css/glightbox.min.css" />
    <link rel="stylesheet" href="/assets/neon/css/simplebar.min.css" />
    <link rel="preload" href="/assets/neon/css/theme.min.css" as="style" />
    <link rel="stylesheet" href="/assets/neon/css/theme.min.css" id="theme-styles" />
</head>
@include('neon.layouts._nav')
@yield('content')
@include('neon.layouts._footer')
</body>
@include('neon.layouts._script')
@section('js')
@show
</html>
