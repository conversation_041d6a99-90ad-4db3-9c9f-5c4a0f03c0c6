@extends('neon.layouts.default')

@section('content')
<style>
    .equal-height {
        min-height: 160px;
    }
    
    .stats-icon {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background-color: #f0f0f0;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 10px;
    }
    
    /* 新增样式 */
    .product-search {
        max-width: 250px;
    }
    
    .product-sort {
        cursor: pointer;
    }
    
    .paymentsvg {
        display: inline-block;
        width: 56px;
        height: 56px;
        background-color: #fff;
        padding: 5px;
        border-radius: 0.5rem;
    }
    
    .paymentsvg svg {
        width: 100%;
        height: 100%;
    }
    
    .badge-count {
        background-color: #0d6efd;
        color: #fff;
        font-size: 0.75rem;
        padding: 0.25em 0.5em;
        border-radius: 0.375rem;
    }
</style>

<div class="container py-5">
    <div class="row pt-md-2 pt-lg-3 pb-sm-2 pb-md-3 pb-lg-4 pb-xl-5">
        <!-- Sidebar navigation -->
        <aside class="col-lg-3">
            <div class="offcanvas-lg offcanvas-start pe-lg-0 pe-xl-4" id="accountSidebar">
                <!-- Header -->
                <div class="offcanvas-header d-lg-block py-3 p-lg-0">
                    <div class="d-flex align-items-center">
                        <img src="/assets/neon/banner/0000.webp" 
                             class="d-flex justify-content-center align-items-center flex-shrink-0 bg-body-tertiary rounded-circle mb-0" 
                             style="width: 3rem; height: 3rem">
                        <div class="min-w-0 ps-3">
                            <h5 class="h6 mb-1">{{ Auth::user()->email }}</h5>
                            <div class="nav flex-nowrap text-nowrap min-w-0">
                                <a class="nav-link animate-underline text-body p-0" href="#bonusesModal" data-bs-toggle="modal">
                                    <svg class="text-warning flex-shrink-0 me-2" xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor">
                                        <path d="M1.333 9.667H7.5V16h-5c-.64 0-1.167-.527-1.167-1.167V9.667zm13.334 0v5.167c0 .64-.527 1.167-1.167 1.167h-5V9.667h6.167zM0 5.833V7.5c0 .64.527 1.167 1.167 1.167h.167H7.5v-1-3H1.167C.527 4.667 0 5.193 0 5.833z"></path>
                                        <path d="M8 5.363a.5.5 0 0 1-.495-.573C7.752 3.123 9.054-.03 12.219-.03c1.807.001 2.447.977 2.447 1.813 0 1.486-2.069 3.58-6.667 3.58zM12.219.971c-2.388 0-3.295 2.27-3.595 3.377 1.884-.088 3.072-.565 3.756-.971.949-.563 1.287-1.193 1.287-1.595 0-.599-.747-.811-1.447-.811z"></path>
                                        <path d="M8.001 5.363c-4.598 0-6.667-2.094-6.667-3.58 0-.836.641-1.812 2.448-1.812 3.165 0 4.467 3.153 4.713 4.819a.5.5 0 0 1-.495.573zM3.782.971c-.7 0-1.448.213-1.448.812 0 .851 1.489 2.403 5.042 2.566C7.076 3.241 6.169.971 3.782.971z"></path>
                                    </svg>
                                    <span class="animate-target me-1"></span>
                                    <span class="text-body fw-normal text-truncate">$</span>
                                </a>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn-close d-lg-none" data-bs-dismiss="offcanvas" data-bs-target="#accountSidebar" aria-label="Close"></button>
                </div>
                
                <!-- Body (Navigation) -->
                <div class="offcanvas-body d-block pt-2 pt-lg-4 pb-lg-0">
                    <nav class="list-group list-group-borderless">
                        <a class="list-group-item list-group-item-action d-flex align-items-center pe-none active" href="/user">
                            <i class="ci-grid fs-base opacity-75 me-2"></i>
                            控制面板
                            <span class="badge bg-primary rounded-pill ms-auto badge-count">0</span>
                        </a>
                        <a class="list-group-item list-group-item-action d-flex align-items-center" href="/user/invite">
                            <i class="ci-gift fs-base opacity-75 me-2"></i>
                            返利信息
                        </a>
                        <a class="list-group-item list-group-item-action d-flex align-items-center" href="javascript:void(0);" onclick="showToast('此项目暂未开通');">
                            <i class="ci-credit-card fs-base opacity-75 me-2"></i>
                            付款方式
                        </a>
                        <a class="list-group-item list-group-item-action d-flex align-items-center" href="javascript:void(0);" onclick="showToast('此项目暂未开通');">
                            <i class="ci-star fs-base opacity-75 me-2"></i>
                            我的评论
                        </a>
                    </nav>
                    <h6 class="pt-4 ps-2 ms-1">管理帐户</h6>
                    <nav class="list-group list-group-borderless">
                        <a class="list-group-item list-group-item-action d-flex align-items-center" href="/user/account">
                            <i class="ci-user fs-base opacity-75 me-2"></i>
                            修改密码
                        </a>
                        <a class="list-group-item list-group-item-action d-flex align-items-center" href="javascript:void(0);" onclick="showToast('此项目暂未开通');">
                            <i class="ci-map-pin fs-base opacity-75 me-2"></i>
                            收货地址
                        </a>
                    </nav>
                    <h6 class="pt-4 ps-2 ms-1">客户服务</h6>
                    <nav class="list-group list-group-borderless">
                        <a class="list-group-item list-group-item-action d-flex align-items-center" href="javascript:void(0);" onclick="showToast('此项目暂未开通');">
                            <i class="ci-help-circle fs-base opacity-75 me-2"></i>
                            帮助中心
                        </a>
                        <a class="list-group-item list-group-item-action d-flex align-items-center" href="javascript:void(0);" onclick="showToast('此项目暂未开通');">
                            <i class="ci-info fs-base opacity-75 me-2"></i>
                            条款和隐私
                        </a>
                    </nav>
                    <nav class="list-group list-group-borderless pt-3">
                        <a class="list-group-item list-group-item-action d-flex align-items-center" href="/logout">
                            <i class="ci-log-out fs-base opacity-75 me-2"></i>
                            退出
                        </a>
                    </nav>
                </div>
            </div>
        </aside>

        <!-- Main content -->
        <div class="col-lg-9">
            <!-- Stats -->
            <div class="row g-3 g-xl-4 pb-3 mb-2 mb-sm-3">
                <div class="col-md-4 col-sm-6">
                    <div class="card h-100 bg-success-subtle rounded-4 text-center p-4">
                        <h2 class="fs-sm pb-2 mb-1">账号余额</h2>
                        <div class="h2 pb-1 mb-2">${{ Auth::user()->money }}</div>
                        <button type="button" class="btn btn-sm btn-dark" data-bs-toggle="modal" data-bs-target="#rechargeModal">充值余额</button>
                    </div>
                </div>
                <div class="col-md-4 col-sm-6">
                    <div class="card h-100 bg-info-subtle rounded-4 text-center p-4">
                        <h2 class="fs-sm pb-2 mb-1">{{ Auth::user()->email }}</h2>
                        <div class="h2 pb-1 mb-2">0级代理</div>
                        <a class="btn btn-sm btn-dark" href="/account">修改资料</a>
                    </div>
                </div>
                <div class="col-md-4 col-sm-12">
                    <div class="card h-100 bg-warning-subtle rounded-4 text-center p-4">
                        <h2 class="fs-sm pb-2 mb-1">返利订单</h2>
                        <div class="h2 pb-1 mb-2">{{ $invite_count }}单</div>
                        <a class="btn btn-sm btn-dark" href="/user/invite">查看订单</a>
                    </div>
                </div>
            </div>

            <!-- 订单视图 -->
            <div class="card border rounded-4 py-4 px-3 px-sm-4">
                <div data-filter-list='{"searchClass": "product-search", "listClass": "product-list", "sortClass": "product-sort", "valueNames": ["product", "date", "tendered", "earning"]}'>
                    <div class="d-flex flex-column flex-sm-row align-items-center justify-content-between gap-3 mb-2 mb-sm-3 mb-md-4">
                        <h2 class="h5 text-center text-sm-start mb-0">我的订单信息</h2>
                        <div class="position-relative w-100" style="max-width: 250px;">
                            <i class="ci-search position-absolute top-50 start-0 translate-middle-y ms-3"></i>
                            <input type="search" class="product-search form-control form-icon-start rounded-pill" placeholder="搜索商品">
                            <button class="btn btn-sm btn-outline-secondary w-auto border-0 p-1 position-absolute top-50 end-0 translate-middle-y me-2 opacity-0">
                                <svg class="opacity-75" width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M18.619 5.381a.875.875 0 0 1 0 1.238l-12 12A.875.875 0 0 1 5.38 17.38l12-12a.875.875 0 0 1 1.238 0Z"></path>
                                    <path d="M5.381 5.381a.875.875 0 0 1 1.238 0l12 12a.875.875 0 1 1-1.238 1.238l-12-12a.875.875 0 0 1 0-1.238Z"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    <table class="table align-middle fs-sm mb-0">
                        <thead>
                            <tr>
                                <th class="ps-0" scope="col">
                                    <span class="fw-normal text-body">商品类型</span>
                                </th>
                                <th class="d-none d-md-table-cell" scope="col">
                                    <span class="fw-normal text-body">状态</span>
                                </th>
                                <th class="d-none d-md-table-cell" scope="col">
                                    <button type="button" class="btn fw-normal text-body product-sort p-0" data-sort="date">时间</button>
                                </th>
                                <th class="text-end d-none d-sm-table-cell" scope="col">
                                    <button type="button" class="btn fw-normal text-body product-sort p-0 me-n2" data-sort="tendered">订单号</button>
                                </th>
                                <th class="text-end pe-0" scope="col">
                                    <button type="button" class="btn fw-normal text-body product-sort p-0 me-n2" data-sort="earning">价格</button>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="product-list">
                            @foreach ($orders as $order)
                            <tr>
                                <td class="product d-none d-sm-table-cell">{{ $order->order_sn }}</td>
                                <td class="d-none d-sm-table-cell">{{ $order->title }}</td>
                                <td class="tendered">${{ $order->price }}</td>
                                <td class="text-end d-none d-sm-table-cell">{{ $order->order_sn }}</td>
                                <td class="text-end earning">${{ $order->price }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>

                    <!-- Pagination -->
                    <div class="d-flex align-items-center justify-content-between pt-4 gap-3">
                        <div class="fs-sm">每个页面显示所有订单中的 <span class="fw-semibold">10件</span><span class="d-none d-sm-inline"> 商品</span></div>
                        <nav aria-label="Pagination">
                            <ul class="pagination">
                                <li class="page-item active">
                                    <a class="page-link" href="#">1</a>
                                </li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 充值模态框 -->
<div class="modal fade" id="rechargeModal" data-bs-backdrop="static" tabindex="-1" aria-labelledby="rechargeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-dialog-scrollable">
        <form class="modal-content needs-validation" id="buy-form" action="{{ url('/user/recharge-money') }}" method="post" novalidate>
            @csrf
            <div class="modal-header border-0">
                <h5 class="modal-title" id="rechargeModalLabel">余额充值</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body pb-3 pt-0">
                <div class="d-flex flex-column alert alert-success text-dark-emphasis fs-sm border-0 rounded-4 mb-0" role="alert">
                    @if($recharge_promotion)
                    @foreach($recharge_promotion as $key => $item)
                    <div class="d-flex justify-content-between" data-key="{{ $key }}" data-amount="{{ $item['amount'] }}">
                        充${{ $item['amount'] }}
                        <div class="d-flex justify-content-between">
                            送${{ $item['value'] }}
                        </div>
                    </div>
                    @endforeach
                    @endif
                </div>
            </div>
            <div class="modal-body pb-3 pt-0">
                <div class="mb-3">
                    <label for="review-name" class="form-label">充值金额 <span class="text-danger">*</span></label>
                    <input id="number" type="number" name="amount" min="1" class="form-control" placeholder="请输入需要充值的金额" autocomplete="off" required>
                    <div class="invalid-feedback">请输入您的充值金额!</div>
                </div>
                <div class="mb-4">
                    <label class="form-label fw-semibold pb-1 mb-2">
                        支付方式<span class="text-danger">*</span>
                    </label>
                    <div class="d-flex flex-wrap gap-2" data-binded-label="#colorOption" id="paymentGroup">
                        @foreach($payways as $key => $way)
                        <label class="payments" data-type="{{ $way['pay_check'] }}" data-id="{{ $way['id'] }}" data-name="{{ $way['pay_name'] }}" data-label="{{ $way['pay_name'] }}">
                            <input required type="radio" name="payway" value="{{ $way['id'] }}" class="btn-check" id="payway-{{ $key }}" @if($key == 0) checked @endif>
                            <span class="btn btn-image p-0 paymentsvg">
                                <!-- 支付图标占位符 -->
                                @if($way['pay_check'] == 'alipay')
                                <svg width="56" fill="#027AFF" viewBox="-2.4 -2.4 28.80 28.80" role="img" xmlns="http://www.w3.org/2000/svg">...</svg>
                                @elseif($way['pay_check'] == 'wxpay')
                                <svg width="56" xmlns="http://www.w3.org/2000/svg" aria-label="WeChat" role="img" viewBox="-51.2 -51.2 614.40 614.40" fill="#ffffff" stroke="#ffffff">...</svg>
                                @elseif($way['pay_check'] == 'binance')
                                <svg width="56" viewBox="-3.2 -3.2 38.40 38.40" xmlns="http://www.w3.org/2000/svg fill="#000000">...</svg>
                                @elseif($way['pay_check'] == 'tokenpay-trx')
                                <svg width="56" viewBox="-3.2 -3.2 38.40 38.40" xmlns="http://www.w3.org/2000/svg" fill="#000000">...</svg>
                                @elseif($way['pay_check'] == 'tokenpay-usdt-trc')
                                <svg width="56" viewBox="-3.2 -3.2 38.40 38.40" xmlns="http://www.w3.org/2000/svg" fill="#000000">...</svg>
                                @elseif($way['pay_check'] == 'tokenpay-eth')
                                <svg width="56" viewBox="-3.2 -3.2 38.40 38.40" xmlns="http://www.w3.org/2000/svg" fill="#000000">...</svg>
                                @endif
                            </span>
                            <span class="visually-hidden">{{ $way['pay_name'] }}</span>
                        </label>
                        @endforeach
                    </div>
                </div>
                <div class="mt-4 text-center">
                    <button type="submit" class="btn btn-dark" id="submit">
                        <i class="mdi mdi-truck-fast mr-1"></i>
                        点击充值
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

@stop

@section('js')
<script>
document.addEventListener('DOMContentLoaded', function() {
    @if(session('success'))
    $.NotificationApp.send("Success", "{{ session('success') }}", "top-right", "rgba(0,0,0,0.2)", "success");
    @endif

    @if($errors->any())
    var firstError = "{{ $errors->all()[0] }}";
    $.NotificationApp.send("Error", firstError, "top-right", "rgba(0,0,0,0.2)", "error");
    @endif
});

$(document).ready(function() {
    // 支付方式标签点击事件
    $('.payments').click(function() {
        $('.payments').removeClass('active');
        $(this).addClass('active');
        $('#paymentGroup input[name="payway"]').val($(this).data('id'));
    });
    
    // 充值金额标签点击事件
    $('.tag').click(function() {
        $('.tag').removeClass('active');
        $(this).toggleClass("active");
        $('input[name=amount]').val($(this).data('amount'));
    });
    
    // 支付方式初始化
    updateSubmitButtonState();
    
    // 更新提交按钮状态
    function updateSubmitButtonState() {
        if ($('input[name="payway"]').val() == 0 || $('input[name="payway"]').val() == "") {
            $('#submit').prop('disabled', true).addClass('btn-disabled');
        } else {
            $('#submit').prop('disabled', false).removeClass('btn-disabled');
        }
    }
    
    // 提交按钮点击验证
    $('#submit').click(function() {
        if ($("input[name='amount']").val() <= 0) {
            $.NotificationApp.send("警告！", "请输入正确的金额~", "top-center", "rgba(0,0,0,0.2)", "info");
            return false;
        }
    });
});
</script>
@stop
