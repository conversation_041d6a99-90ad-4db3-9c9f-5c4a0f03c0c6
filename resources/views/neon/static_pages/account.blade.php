@extends('neon.layouts.default')

@section('content')
<!-- 页面标题 -->
<div class="d-flex justify-content-between align-items-center mb-4">
  <h1 class="h2 pb-1 pb-sm-2 pb-md-3 mb-0">个人设置</h1>
  <div class="d-none d-md-block">
    <span class="badge bg-primary rounded-pill px-3 py-2">
      <i class="ci-user me-1"></i> {{ auth()->user()->username }}
    </span>
  </div>
</div>

<!-- 导航选项卡 -->
<div class="overflow-auto mb-3">
  <ul class="nav nav-pills flex-nowrap gap-2 text-nowrap pb-3" role="tablist">
    <li class="nav-item" role="presentation">
      <button type="button" class="nav-link active" id="profile-tab" data-bs-toggle="pill" data-bs-target="#profile" role="tab" aria-controls="profile" aria-selected="true">
        <i class="ci-user me-1 d-none d-sm-inline"></i>个人资料
      </button>
    </li>
    <li class="nav-item" role="presentation">
      <button type="button" class="nav-link" id="security-tab" data-bs-toggle="pill" data-bs-target="#security" role="tab" aria-controls="security" aria-selected="false" tabindex="-1">
        <i class="ci-lock me-1 d-none d-sm-inline"></i>密码设置
      </button>
    </li>
    <li class="nav-item" role="presentation">
      <button type="button" class="nav-link" id="notifications-tab" data-bs-toggle="pill" data-bs-target="#notifications" role="tab" aria-controls="notifications" aria-selected="false" tabindex="-1">
        <i class="ci-bell me-1 d-none d-sm-inline"></i>提醒通知
      </button>
    </li>
  </ul>
</div>

<!-- 标签内容 -->
<div class="tab-content bg-white rounded-3 shadow-sm p-3 p-md-4">
  <!-- 个人资料选项卡 -->
  <div class="tab-pane fade show active" id="profile" role="tabpanel" aria-labelledby="profile-tab">
    <!-- 用户图片 -->
    <div class="d-flex flex-column flex-md-row align-items-start align-items-md-center pb-3 mb-3 border-bottom">
      <div class="position-relative me-md-4 mb-3 mb-md-0">
        <div class="ratio ratio-1x1 hover-effect-opacity border rounded-circle overflow-hidden" style="width: 112px">
          <img src="https://fak.app/uploads/images/be8039d562b7d33c13873b614b605f79.webp" alt="用户头像" class="object-fit-cover">
          <div class="hover-effect-target position-absolute top-0 start-0 d-flex align-items-center justify-content-center w-100 h-100 opacity-0">
            <button type="button" class="btn btn-icon btn-sm btn-light position-relative z-2" aria-label="删除图片">
              <i class="ci-trash fs-base"></i>
            </button>
            <span class="position-absolute top-0 start-0 w-100 h-100 bg-black bg-opacity-25 z-1"></span>
          </div>
        </div>
        <span class="position-absolute bottom-0 end-0 bg-success rounded-circle border border-3 border-white" style="width: 16px; height: 16px;"></span>
      </div>
      <div class="ps-md-0">
        <h5 class="mb-2">头像设置</h5>
        <p class="fs-sm mb-3" style="max-width: 400px">您的个人资料图片将显示在您的个人资料和列表中。PNG 或 JPG 的宽度和高度不得大于 500px</p>
        <div class="d-flex gap-2">
          <button type="button" class="btn btn-sm btn-primary rounded-pill">
            <i class="ci-cloud-upload me-2"></i>上传图片
          </button>
          <button type="button" class="btn btn-sm btn-outline-secondary rounded-pill">
            <i class="ci-reload me-2"></i>重置
          </button>
        </div>
      </div>
    </div>

    <!-- 个人资料表单 -->
    <form class="needs-validation" novalidate method="POST" action="{{ route('user.profile.update') }}">
      @csrf
      <div class="row g-4 mb-4">
        <div class="col-12 col-md-6 position-relative">
          <label for="email" class="form-label d-flex align-items-center fs-base">邮箱地址 *</label>
          <div class="input-group">
            <input type="email" class="form-control form-control-lg rounded-pill" id="email" name="email" value="{{ auth()->user()->email }}" required>
            @if(auth()->user()->email_verified_at)
              <span class="input-group-text bg-success text-white border-0 rounded-pill">已验证</span>
            @else
              <span class="input-group-text bg-warning text-dark border-0 rounded-pill">未验证</span>
            @endif
          </div>
          <div class="invalid-feedback">请输入有效的邮箱地址</div>
          @unless(auth()->user()->email_verified_at)
            <div class="mt-2">
              <button type="button" class="btn btn-sm btn-outline-primary rounded-pill">
                <i class="ci-send me-1"></i>发送验证邮件
              </button>
            </div>
          @endunless
        </div>
        
        <div class="col-12 col-md-6">
          <label for="username" class="form-label fs-base">用户名</label>
          <input type="text" class="form-control form-control-lg rounded-pill" id="username" name="username" value="{{ auth()->user()->username }}" required>
          <div class="invalid-feedback">请输入用户名</div>
        </div>
        
        <div class="col-12">
          <label for="description" class="form-label fs-base">个人简介</label>
          <textarea class="form-control form-control-lg rounded-3" id="description" name="description" rows="4" placeholder="介绍一下你自己...">{{ auth()->user()->bio ?? '' }}</textarea>
          <div class="form-text">最多500个字符</div>
        </div>
      </div>
      
      <div class="d-flex gap-3 pt-2">
        <button type="submit" class="btn btn-lg btn-primary rounded-pill px-4">
          <i class="ci-save me-2"></i>保存更改
        </button>
        <button type="reset" class="btn btn-lg btn-outline-secondary rounded-pill px-4">
          <i class="ci-close me-2"></i>取消
        </button>
      </div>
    </form>
  </div>

  <!-- 密码设置选项卡 -->
  <div class="tab-pane fade" id="security" role="tabpanel" aria-labelledby="security-tab">
    <!-- 密码修改表单 -->
    <form class="needs-validation" id="change-password-form" method="post" novalidate>
      @csrf
      <div class="alert alert-info mb-4">
        <i class="ci-info-circle me-2"></i> 为了您的账户安全，请定期更新密码并确保密码强度
      </div>
      
      <div class="row g-4 mb-4">
        <div class="col-12 col-md-6">
          <label for="current-password" class="form-label fs-base">当前密码</label>
          <div class="password-toggle">
            <input type="password" class="form-control form-control-lg rounded-pill" id="current-password" name="current_password" required>
            <label class="password-toggle-btn" aria-label="显示/隐藏密码">
              <input type="checkbox" class="password-toggle-check">
              <span class="password-toggle-indicator"></span>
            </label>
            <div class="invalid-feedback">请输入当前密码</div>
          </div>
        </div>
      </div>
      
      <div class="row g-4 mb-4">
        <div class="col-12 col-md-6">
          <label for="new-password" class="form-label fs-base">新密码 
            <span class="fs-sm fw-normal text-body-secondary">(至少8个字符)</span>
          </label>
          <div class="password-toggle">
            <input type="password" class="form-control form-control-lg rounded-pill" minlength="8" id="new-password" name="new_password" required>
            <label class="password-toggle-btn" aria-label="显示/隐藏密码">
              <input type="checkbox" class="password-toggle-check">
              <span class="password-toggle-indicator"></span>
            </label>
            <div class="password-strength mt-2">
              <div class="progress" style="height: 5px">
                <div class="progress-bar bg-danger" role="progressbar" style="width: 25%"></div>
              </div>
              <div class="form-text">密码强度：弱</div>
            </div>
          </div>
        </div>
        
        <div class="col-12 col-md-6">
          <label for="confirm-new-password" class="form-label fs-base">确认新密码</label>
          <div class="password-toggle">
            <input type="password" class="form-control form-control-lg rounded-pill" minlength="8" id="confirm-new-password" name="new_password_confirmation" required>
            <label class="password-toggle-btn" aria-label="显示/隐藏密码">
              <input type="checkbox" class="password-toggle-check">
              <span class="password-toggle-indicator"></span>
            </label>
            <div class="invalid-feedback">两次输入的密码不一致</div>
          </div>
        </div>
      </div>
      
      <div class="d-flex gap-3 pt-2">
        <button type="submit" class="btn btn-lg btn-primary rounded-pill px-4">
          <i class="ci-lock me-2"></i>更新密码
        </button>
        <button type="reset" class="btn btn-lg btn-outline-secondary rounded-pill px-4">
          <i class="ci-close me-2"></i>取消
        </button>
      </div>
    </form>
  </div>

  <!-- 通知设置选项卡 -->
  <div class="tab-pane fade" id="notifications" role="tabpanel" aria-labelledby="notifications-tab">
    <div class="alert alert-info mb-4">
      <i class="ci-info-circle me-2"></i> 您可以根据需要启用或禁用以下通知类型
    </div>
    
    <form id="notification-form">
      <div class="vstack gap-4">
        <div class="form-check form-switch p-3 bg-light rounded-3">
          <input type="checkbox" class="form-check-input" id="product-sold" checked>
          <label class="form-check-label ps-3" for="product-sold">
            <span class="d-block h6 mb-1">产品售出通知</span>
            <span class="d-block fs-sm text-muted">当有人购买我的某个产品时发送一封电子邮件。</span>
          </label>
        </div>
        
        <div class="form-check form-switch p-3 bg-light rounded-3">
          <input type="checkbox" class="form-check-input" id="product-update" checked>
          <label class="form-check-label ps-3" for="product-update">
            <span class="d-block h6 mb-1">产品更新通知</span>
            <span class="d-block fs-sm text-muted">当我购买的产品更新时发送电子邮件。</span>
          </label>
        </div>
        
        <div class="form-check form-switch p-3 bg-light rounded-3">
          <input type="checkbox" class="form-check-input" id="surveys">
          <label class="form-check-label ps-3" for="surveys">
            <span class="d-block h6 mb-1">调查和测试</span>
            <span class="d-block fs-sm text-muted">接收参与调查、咨询和工具测试的邀请。</span>
          </label>
        </div>
        
        <div class="form-check form-switch p-3 bg-light rounded-3">
          <input type="checkbox" class="form-check-input" id="product-review" checked>
          <label class="form-check-label ps-3" for="product-review">
            <span class="d-block h6 mb-1">产品评论通知</span>
            <span class="d-block fs-sm text-muted">公司新闻和合作提议。</span>
          </label>
        </div>
        
        <div class="form-check form-switch p-3 bg-light rounded-3">
          <input type="checkbox" class="form-check-input" id="daily-summary">
          <label class="form-check-label ps-3" for="daily-summary">
            <span class="d-block h6 mb-1">每日摘要邮件</span>
            <span class="d-block fs-sm text-muted">当有人留下带有其评分的评论时发送电子邮件。</span>
          </label>
        </div>
        
        <div class="form-check form-switch p-3 bg-light rounded-3">
          <input type="checkbox" class="form-check-input" id="security-alerts" checked>
          <label class="form-check-label ps-3" for="security-alerts">
            <span class="d-block h6 mb-1">安全提醒</span>
            <span class="d-block fs-sm text-muted">账户有异常活动时发送通知。</span>
          </label>
        </div>
        
        <div class="d-flex gap-3 pt-3">
          <button type="submit" class="btn btn-lg btn-primary rounded-pill px-4">
            <i class="ci-save me-2"></i>保存设置
          </button>
          <button type="reset" class="btn btn-lg btn-outline-secondary rounded-pill px-4">
            <i class="ci-reload me-2"></i>重置
          </button>
        </div>
      </div>
    </form>
  </div>
</div>

@push('scripts')
<script>
  // 表单验证逻辑
  (function () {
    'use strict'
    
    // 获取所有需要验证的表单
    var forms = document.querySelectorAll('.needs-validation')
    
    // 循环处理每个表单
    Array.prototype.slice.call(forms).forEach(function (form) {
      form.addEventListener('submit', function (event) {
        if (!form.checkValidity()) {
          event.preventDefault()
          event.stopPropagation()
        }
        
        form.classList.add('was-validated')
      }, false)
    })
    
    // 密码确认验证
    var passwordForm = document.getElementById('change-password-form');
    if (passwordForm) {
      passwordForm.addEventListener('submit', function(event) {
        var password = document.getElementById('new-password');
        var confirmPassword = document.getElementById('confirm-new-password');
        
        if (password.value !== confirmPassword.value) {
          confirmPassword.setCustomValidity("两次输入的密码不一致");
          confirmPassword.reportValidity();
          event.preventDefault();
          event.stopPropagation();
        } else {
          confirmPassword.setCustomValidity("");
        }
        
        passwordForm.classList.add('was-validated');
      });
    }
    
    // 密码强度检测
    var passwordInput = document.getElementById('new-password');
    if (passwordInput) {
      passwordInput.addEventListener('input', function() {
        var strengthBar = document.querySelector('.password-strength .progress-bar');
        var strengthText = document.querySelector('.password-strength .form-text');
        var password = passwordInput.value;
        var strength = 0;
        
        if (password.length > 7) strength += 25;
        if (password.match(/[a-z]+/)) strength += 25;
        if (password.match(/[A-Z]+/)) strength += 25;
        if (password.match(/[0-9]+/)) strength += 25;
        
        strengthBar.style.width = strength + '%';
        
        if (strength < 50) {
          strengthBar.className = 'progress-bar bg-danger';
          strengthText.textContent = '密码强度：弱';
        } else if (strength < 75) {
          strengthBar.className = 'progress-bar bg-warning';
          strengthText.textContent = '密码强度：中等';
        } else {
          strengthBar.className = 'progress-bar bg-success';
          strengthText.textContent = '密码强度：强';
        }
      });
    }
    
    // 密码显示/隐藏功能
    var toggleButtons = document.querySelectorAll('.password-toggle-btn');
    toggleButtons.forEach(function(button) {
      button.addEventListener('click', function() {
        var input = this.previousElementSibling;
        var type = input.getAttribute('type') === 'password' ? 'text' : 'password';
        input.setAttribute('type', type);
      });
    });
  })();
</script>
@endpush
@endsection