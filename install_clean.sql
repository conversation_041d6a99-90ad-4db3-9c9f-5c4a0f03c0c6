-- Laravel 10 + PHP 8.2 升级后的数据库结构
-- 这是一个现代化的数据库结构，兼容 Laravel 10

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- 管理员表
DROP TABLE IF EXISTS `admin_users`;
CREATE TABLE `admin_users` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `username` varchar(190) COLLATE utf8mb4_unicode_ci NOT NULL,
  `password` varchar(60) COLLATE utf8mb4_unicode_ci NOT NULL,
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
  `avatar` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `remember_token` varchar(100) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `admin_users_username_unique` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 商品分组表
DROP TABLE IF EXISTS `goods_group`;
CREATE TABLE `goods_group` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `gp_name` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '分类名称',
  `picture` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '分类图标',
  `is_open` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用，1是 0否',
  `ord` int NOT NULL DEFAULT '1' COMMENT '排序权重 越大越靠前',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `goods_group_is_open_index` (`is_open`),
  KEY `goods_group_ord_index` (`ord`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品分类表';

-- 商品表
DROP TABLE IF EXISTS `goods`;
CREATE TABLE `goods` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `remote_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '远程商品ID',
  `source_site_id` bigint unsigned DEFAULT NULL COMMENT '来源站点ID',
  `remote_data` json DEFAULT NULL COMMENT '远程数据',
  `last_sync_at` timestamp NULL DEFAULT NULL COMMENT '最后同步时间',
  `group_id` bigint unsigned NOT NULL DEFAULT '1' COMMENT '商品分组ID',
  `gd_name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '商品名称',
  `gd_description` text COLLATE utf8mb4_unicode_ci COMMENT '商品描述',
  `gd_keywords` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '商品关键词',
  `retail_price` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '零售价',
  `picture` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '商品图片',
  `price` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '实际售价',
  `preselection` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '预选加价',
  `stock` int NOT NULL DEFAULT '0' COMMENT '库存数量',
  `sales_volume` int NOT NULL DEFAULT '0' COMMENT '销售数量',
  `ord` int NOT NULL DEFAULT '1' COMMENT '排序权重',
  `payment_limit` json DEFAULT NULL COMMENT '支付方式限制',
  `buy_limit_num` int NOT NULL DEFAULT '0' COMMENT '购买限制数量',
  `min_buy_num` int NOT NULL DEFAULT '0' COMMENT '最低购买数量',
  `buy_prompt` text COLLATE utf8mb4_unicode_ci COMMENT '购买提示',
  `description` longtext COLLATE utf8mb4_unicode_ci COMMENT '商品详情',
  `type` tinyint NOT NULL DEFAULT '1' COMMENT '商品类型：1自动发货，2人工处理',
  `is_sub` tinyint NOT NULL DEFAULT '0' COMMENT '是否多规格：0否，1是',
  `wholesale_price_cnf` text COLLATE utf8mb4_unicode_ci COMMENT '批发价格配置',
  `other_ipu_cnf` text COLLATE utf8mb4_unicode_ci COMMENT '其他输入配置',
  `api_hook` bigint unsigned DEFAULT NULL COMMENT 'API钩子',
  `is_open` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否上架：1是，0否',
  `open_rebate` tinyint NOT NULL DEFAULT '0' COMMENT '是否开启返利',
  `grade_0` decimal(8,2) DEFAULT NULL COMMENT '等级0价格',
  `grade_1` decimal(8,2) DEFAULT NULL COMMENT '等级1价格',
  `grade_2` decimal(8,2) DEFAULT NULL COMMENT '等级2价格',
  `grade_3` decimal(8,2) DEFAULT NULL COMMENT '等级3价格',
  `min_buy_count` int DEFAULT NULL COMMENT '最小购买数量',
  `max_buy_count` int DEFAULT NULL COMMENT '最大购买数量',
  `rebate_rate` decimal(5,2) NOT NULL DEFAULT '0.00' COMMENT '返利率',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `goods_group_id_is_open_index` (`group_id`,`is_open`),
  KEY `goods_type_is_open_index` (`type`,`is_open`),
  KEY `goods_ord_index` (`ord`),
  KEY `goods_remote_id_index` (`remote_id`),
  KEY `goods_source_site_id_index` (`source_site_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品表';

-- 商品子规格表
DROP TABLE IF EXISTS `goods_sub`;
CREATE TABLE `goods_sub` (
  `id` bigint unsigned NOT NULL AUTO_INCREMENT,
  `goods_id` bigint unsigned NOT NULL COMMENT '商品ID',
  `name` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '规格名称',
  `price` decimal(8,2) NOT NULL DEFAULT '0.00' COMMENT '规格价格',
  `stock` int NOT NULL DEFAULT '0' COMMENT '规格库存',
  `sales_volume` int NOT NULL DEFAULT '0' COMMENT '规格销量',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：0禁用，1启用',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `goods_sub_goods_id_index` (`goods_id`),
  KEY `goods_sub_status_index` (`status`),
  CONSTRAINT `goods_sub_goods_id_foreign` FOREIGN KEY (`goods_id`) REFERENCES `goods` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='商品子规格表';

-- 插入默认数据
INSERT INTO `goods_group` (`id`, `gp_name`, `is_open`, `ord`, `created_at`, `updated_at`) VALUES
(1, '默认分组', 1, 1, NOW(), NOW());

-- 插入默认管理员
INSERT INTO `admin_users` (`id`, `username`, `password`, `name`, `created_at`, `updated_at`) VALUES
(1, 'admin', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'Administrator', NOW(), NOW());

SET FOREIGN_KEY_CHECKS = 1;
